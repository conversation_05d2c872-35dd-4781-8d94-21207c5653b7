[gd_scene load_steps=2 format=3 uid="uid://dtlwk8wdeal3h"]

[ext_resource type="Texture2D" uid="uid://o2chsr07oeqs" path="res://addons/fmod/icons/bank_icon.svg" id="1_11c48"]

[node name="FmodBankPathsPropertyEditorUi" type="VBoxContainer"]
offset_right = 92.0
offset_bottom = 43.0
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="AddButton" type="Button" parent="HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "+"
icon = ExtResource("1_11c48")
icon_alignment = 2

[node name="RemoveButton" type="Button" parent="HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "-"

[node name="VSeparator" type="VSeparator" parent="HBoxContainer"]
layout_mode = 2

[node name="UpButton" type="Button" parent="HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "↑"

[node name="DownButton" type="Button" parent="HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "↓"

[node name="BankList" type="ItemList" parent="."]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
auto_height = true

[node name="HBoxContainer2" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="ManualAddLineEdit" type="LineEdit" parent="HBoxContainer2"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3

[node name="ManualAddButton" type="Button" parent="HBoxContainer2"]
unique_name_in_owner = true
layout_mode = 2
text = "+"
