[configuration]
entry_symbol = "fmod_library_init"
compatibility_minimum = 4.2

[libraries]
windows.editor.x86_64 = "res://addons/fmod/libs/windows/libGodotFmod.windows.editor.x86_64.dll"
windows.debug.x86_64 = "res://addons/fmod/libs/windows/libGodotFmod.windows.template_debug.x86_64.dll"
windows.release.x86_64 = "res://addons/fmod/libs/windows/libGodotFmod.windows.template_release.x86_64.dll"
macos.editor = "res://addons/fmod/libs/macos/libGodotFmod.macos.editor.framework"
macos.debug  = "res://addons/fmod/libs/macos/libGodotFmod.macos.template_debug.framework"
macos.release  = "res://addons/fmod/libs/macos/libGodotFmod.macos.template_release.framework"
linux.editor.x86_64 = "res://addons/fmod/libs/linux/libGodotFmod.linux.editor.x86_64.so"
linux.debug.x86_64 = "res://addons/fmod/libs/linux/libGodotFmod.linux.template_debug.x86_64.so"
linux.release.x86_64 = "res://addons/fmod/libs/linux/libGodotFmod.linux.template_release.x86_64.so"
android.debug.x86_64 = "res://addons/fmod/libs/android/x86_64/libGodotFmod.android.template_debug.x86_64.so"
android.release.x86_64 = "res://addons/fmod/libs/android/x86_64/libGodotFmod.android.template_release.x86_64.so"
android.debug.arm64 = "res://addons/fmod/libs/android/arm64/libGodotFmod.android.template_debug.arm64.so"
android.release.arm64 = "res://addons/fmod/libs/android/arm64/libGodotFmod.android.template_release.arm64.so"
ios.debug  = "res://addons/fmod/libs/ios/libGodotFmod.ios.template_debug.xcframework"
ios.release  = "res://addons/fmod/libs/ios/libGodotFmod.ios.template_release.xcframework"

[icons]
FmodEventEmitter2D = "res://addons/fmod/icons/fmod_icon.svg"
FmodEventEmitter3D = "res://addons/fmod/icons/fmod_icon.svg"
FmodListener2D = "res://addons/fmod/icons/fmod_icon.svg"
FmodListener3D = "res://addons/fmod/icons/fmod_icon.svg"
FmodBankLoader = "res://addons/fmod/icons/fmod_icon.svg"

[dependencies]
windows.editor.x86_64 = {
"libs/windows/fmodL.dll": "",
"libs/windows/fmodstudioL.dll": ""
}
windows.debug.x86_64 = {
"libs/windows/fmodL.dll": "", 
"libs/windows/fmodstudioL.dll": ""
}
windows.release.x86_64 = {
"libs/windows/fmod.dll": "", 
"libs/windows/fmodstudio.dll": ""
}
linux.editor.x86_64  = {
"libs/linux/libfmodL.so": "",
"libs/linux/libfmodL.so.14": "",
"libs/linux/libfmodL.so.14.6": "",
"libs/linux/libfmodstudioL.so": "",
"libs/linux/libfmodstudioL.so.14": "",
"libs/linux/libfmodstudioL.so.14.6": ""
}
linux.debug.x86_64  = {
"libs/linux/libfmodL.so": "",
"libs/linux/libfmodL.so.14": "",
"libs/linux/libfmodL.so.14.6": "",
"libs/linux/libfmodstudioL.so": "",
"libs/linux/libfmodstudioL.so.14": "",
"libs/linux/libfmodstudioL.so.14.6": ""
}
linux.release.x86_64 = {
"libs/linux/libfmod.so": "",
"libs/linux/libfmod.so.14": "",
"libs/linux/libfmod.so.14.6": "",
"libs/linux/libfmodstudio.so": "",
"libs/linux/libfmodstudio.so.14": "",
"libs/linux/libfmodstudio.so.14.6": ""
}
macos.editor  = {
"libs/macos/libfmodL.dylib": "",
"libs/macos/libfmodstudioL.dylib": ""
}
macos.debug  = {
"libs/macos/libfmodL.dylib": "",
"libs/macos/libfmodstudioL.dylib": ""
}
macos.release = {
"libs/macos/libfmod.dylib": "",
"libs/macos/libfmodstudio.dylib": ""
}
android.debug.x86_64 = {
"libs/android/x86_64/libfmodL.so": "",
"libs/android/x86_64/libfmodstudioL.so": "",
}
android.release.x86_64 = {
"libs/android/x86_64/libfmod.so": "",
"libs/android/x86_64/libfmodstudio.so": "",
}
android.debug.arm64 = {
"libs/android/arm64/libfmodL.so": "",
"libs/android/arm64/libfmodstudioL.so": "",
}
android.release.arm64 = {
"libs/android/arm64/libfmod.so": "",
"libs/android/arm64/libfmodstudio.so": "",
}
ios.debug  = {
"libs/ios/libfmodL_iphoneos.a": "",
"libs/ios/libfmodstudioL_iphoneos.a": ""
}
ios.release = {
"libs/ios/libfmod_iphoneos.a": "",
"libs/ios/libfmodstudio_iphoneos.a": ""
}