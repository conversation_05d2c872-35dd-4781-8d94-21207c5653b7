; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[Fmod]

General/auto_initialize=true
General/channel_count=1024
General/is_live_update_enabled=true
General/is_memory_tracking_enabled=false
"Software Format/sample_rate"=48000
"Software Format/speaker_mode"=3
"Software Format/raw_speaker_count"=0
General/default_listener_count=1
General/banks_path="res://fmod/Desktop"
Plugins/path_to_plugin_configuration=""
General/should_load_by_name=false
DSP/dsp_buffer_size=512
DSP/dsp_buffer_count=4
"3D Settings/doppler_scale"=1.0
"3D Settings/distance_factor"=1.0
"3D Settings/rolloff_scale"=1.0

[application]

config/name="Soup Simple"
run/main_scene="uid://dnxgjm6x2bq08"
config/features=PackedStringArray("4.4", "GL Compatibility")
config/icon="res://src/icon.svg"

[autoload]

GameSettings="*res://src/game_settings.tscn"
InputManager="*res://src/input_manager.gd"
SceneManager="*res://src/scene_manager.gd"
Steamworks="*res://src/steamworks.gd"
TargetingService="*res://src/targeting_service.gd"
FmodManager="*res://addons/fmod/FmodManager.gd"

[debug]

file_logging/enable_file_logging=true
gdscript/warnings/untyped_declaration=2
gdscript/warnings/unsafe_property_access=2
gdscript/warnings/unsafe_method_access=2
gdscript/warnings/unsafe_cast=1
gdscript/warnings/unsafe_call_argument=2

[display]

window/size/viewport_width=1920
window/size/viewport_height=1080
window/stretch/mode="canvas_items"
window/stretch/aspect="expand"

[editor_plugins]

enabled=PackedStringArray("res://addons/fmod/plugin.cfg")

[input]

p1_create={
"deadzone": 0.2,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":90,"key_label":0,"unicode":1103,"location":0,"echo":false,"script":null)
]
}
p1_recall={
"deadzone": 0.2,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":88,"key_label":0,"unicode":1095,"location":0,"echo":false,"script":null)
]
}
p2_create={
"deadzone": 0.2,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":78,"key_label":0,"unicode":1090,"location":0,"echo":false,"script":null)
]
}
p2_recall={
"deadzone": 0.2,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":77,"key_label":0,"unicode":1100,"location":0,"echo":false,"script":null)
]
}
mp_move_up={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":87,"key_label":0,"unicode":119,"location":0,"echo":false,"script":null)
, Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":4194320,"key_label":0,"unicode":0,"location":0,"echo":false,"script":null)
]
}
mp_move_down={
"deadzone": 0.5,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":83,"key_label":0,"unicode":115,"location":0,"echo":false,"script":null)
, Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":-1,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":4194322,"key_label":0,"unicode":0,"location":0,"echo":false,"script":null)
]
}

[layer_names]

2d_physics/layer_1="player1"
2d_physics/layer_2="player2"
2d_physics/layer_3="food"
2d_physics/layer_4="virus"

[rendering]

renderer/rendering_method="gl_compatibility"
renderer/rendering_method.mobile="gl_compatibility"
textures/vram_compression/import_etc2_astc=true

[steam]

initialization/app_id=436480
