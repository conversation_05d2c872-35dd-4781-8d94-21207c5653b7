const STEAM_WEB_API_KEY = "YOUR_STEAM_WEB_API_KEY_HERE";
const APP_ID = 436480;

interface FindMatchMessage {
  type: "findMatch";
  ticket: string;
  steamId: string;
}

interface QueuedMessage {
  type: "queued";
  message: string;
}

interface MatchFoundMessage {
  type: "matchFound";
  gameServerUrl: string;
  matchId: string;
}

interface ErrorMessage {
  type: "error";
  message: string;
}

interface QueuedPlayer {
  steamId: string;
  socket: WebSocket;
  timestamp: number;
}

class Matchmaker {
  private queue: QueuedPlayer[] = [];
  private gameServerUrl = "ws://localhost:8001";

  constructor(private port: number) {}

  start() {
    Deno.serve({ port: this.port }, (req) => {
      if (req.headers.get("upgrade") !== "websocket") {
        return new Response(null, { status: 501 });
      }

      const { socket, response } = Deno.upgradeWebSocket(req);
      this.handleConnection(socket);
      return response;
    });

    console.log(`Matchmaker running on port ${this.port}`);
  }

  private handleConnection(socket: WebSocket) {
    console.log("New WebSocket connection established");

    socket.onopen = () => {
      console.log("WebSocket connection opened");
    };

    socket.onmessage = async (event) => {
      try {
        const data = JSON.parse(event.data);
        await this.handleMessage(socket, data);
      } catch (error) {
        console.error("Error parsing message:", error);
        this.sendError(socket, "Invalid message format");
      }
    };

    socket.onclose = () => {
      console.log("WebSocket connection closed");
      this.removePlayerFromQueue(socket);
    };

    socket.onerror = (error) => {
      console.error("WebSocket error:", error);
    };
  }

  private async handleMessage(socket: WebSocket, data: any) {
    if (data.type === "findMatch") {
      await this.handleFindMatch(socket, data as FindMatchMessage);
    } else {
      this.sendError(socket, "Unknown message type");
    }
  }

  private async handleFindMatch(socket: WebSocket, request: FindMatchMessage) {
    console.log(`Matchmaking request from Steam ID: ${request.steamId}`);

    const isValid = await this.verifySteamTicket(request.ticket, request.steamId);
    if (!isValid) {
      this.sendError(socket, "Invalid Steam ticket");
      return;
    }

    const queuedPlayer: QueuedPlayer = {
      steamId: request.steamId,
      socket: socket,
      timestamp: Date.now()
    };

    this.queue.push(queuedPlayer);
    console.log(`Player ${request.steamId} added to queue. Queue size: ${this.queue.length}`);

    this.sendQueued(socket, "Added to queue, waiting for match...");

    if (this.queue.length >= 2) {
      const player1 = this.queue.shift()!;
      const player2 = this.queue.shift()!;

      console.log(`Match found for players ${player1.steamId} and ${player2.steamId}`);

      const matchId = `match_${Date.now()}`;
      this.sendMatchFound(player1.socket, this.gameServerUrl, matchId);
      this.sendMatchFound(player2.socket, this.gameServerUrl, matchId);
    }
  }

  private removePlayerFromQueue(socket: WebSocket) {
    const index = this.queue.findIndex(player => player.socket === socket);
    if (index !== -1) {
      const removedPlayer = this.queue.splice(index, 1)[0];
      console.log(`Player ${removedPlayer.steamId} removed from queue`);
    }
  }

  private sendError(socket: WebSocket, message: string) {
    if (socket.readyState === WebSocket.OPEN) {
      const errorMessage: ErrorMessage = {
        type: "error",
        message: message
      };
      socket.send(JSON.stringify(errorMessage));
    }
  }

  private sendQueued(socket: WebSocket, message: string) {
    if (socket.readyState === WebSocket.OPEN) {
      const queuedMessage: QueuedMessage = {
        type: "queued",
        message: message
      };
      socket.send(JSON.stringify(queuedMessage));
    }
  }

  private sendMatchFound(socket: WebSocket, gameServerUrl: string, matchId: string) {
    if (socket.readyState === WebSocket.OPEN) {
      const matchFoundMessage: MatchFoundMessage = {
        type: "matchFound",
        gameServerUrl: gameServerUrl,
        matchId: matchId
      };
      socket.send(JSON.stringify(matchFoundMessage));
    }
  }

  private async verifySteamTicket(ticket: string, steamId: string): Promise<boolean> {
    if (STEAM_WEB_API_KEY === "YOUR_STEAM_WEB_API_KEY_HERE") {
      console.warn("WARNING: Using mock Steam verification. Set your real Steam Web API key!");
      return true; // Mock verification for testing
    }

    try {
      const url = `https://api.steampowered.com/ISteamUserAuth/AuthenticateUserTicket/v1/`;
      const params = new URLSearchParams({
        key: STEAM_WEB_API_KEY,
        appid: APP_ID.toString(),
        ticket: ticket
      });

      const response = await fetch(`${url}?${params}`);
      const data = await response.json();
      console.log("Steam verification response:", data);

      if (data.response && data.response.params) {
        const verifiedSteamId = data.response.params.steamid;
        console.log("Verified Steam ID:", verifiedSteamId);
        return verifiedSteamId === steamId;
      }

      return false;
    } catch (error) {
      console.error("Error verifying Steam ticket:", error);
      return false;
    }
  }
}

const matchmaker = new Matchmaker(8000);
matchmaker.start();
