# Soup Game Backend

This backend provides multiplayer functionality for the Soup Simple game.

## Requirements

- [Deno](https://deno.land/) installed on your system
- Steam Web API Key (optional, for production)

## Setup

1. **Steam Web API Key (optional):**
   - Get the key at https://steamcommunity.com/dev/apikey
   - Open `matchmaker.ts` and replace `YOUR_STEAM_WEB_API_KEY_HERE` with your key
   - For testing, you can leave it as is - mock verification will be used

## Running

Open two terminals in the `soup-game-backend` folder:

### Terminal 1 - Game Server:
```bash
deno run --allow-net game_server.ts
```

### Terminal 2 - Matchmaker:
```bash
deno run --allow-net matchmaker.ts
```

## Architecture

- **Matchmaker (port 8000):** Handles game search requests, verifies Steam tickets
- **Game Server (port 8001):** Handles game logic and WebSocket connections

## Testing

1. Make sure Steam is running
2. Start both servers
3. Run the Godot game from the editor
4. Export and run a second instance of the game
5. In both clients, click "ONLINE DEMO" -> "Find Game"
6. Control the paddles with A and D keys

## Ports

- 8000: Matchmaker HTTP API
- 8001: Game Server WebSocket

Make sure these ports are available before starting.
