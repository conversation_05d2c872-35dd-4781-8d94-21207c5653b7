interface Paddle {
  y: number;
  height: number;
  speed: number;
}

interface Ball {
  x: number;
  y: number;
  vx: number;
  vy: number;
  radius: number;
}

interface Player {
  id: number;
  paddle: Paddle;
  socket: WebSocket;
  input: 'up' | 'down' | 'stop';
}

interface GameState {
  players: Record<number, Paddle>;
  ball: Ball;
  score: Record<number, number>;
  status: 'waiting' | 'playing' | 'finished';
  gameArea: {
    width: number;
    height: number;
  };
}

class GameServer {
  private players: Map<number, Player> = new Map();
  private gameState: GameState;
  private gameLoop: number | null = null;

  private readonly GAME_WIDTH = 800;
  private readonly GAME_HEIGHT = 600;
  private readonly PADDLE_HEIGHT = 100;
  private readonly PADDLE_SPEED = 5;
  private readonly PADDLE_WIDTH = 20;
  private readonly BALL_RADIUS = 10;
  private readonly BALL_INITIAL_SPEED = 4;
  private readonly MAX_PLAYERS = 2;

  constructor(private port: number) {
    this.initializeGameState();
  }

  private initializeGameState() {
    this.gameState = {
      players: {},
      ball: {
        x: this.GAME_WIDTH / 2,
        y: this.GAME_HEIGHT / 2,
        vx: this.BALL_INITIAL_SPEED * (Math.random() > 0.5 ? 1 : -1),
        vy: this.BALL_INITIAL_SPEED * (Math.random() > 0.5 ? 1 : -1),
        radius: this.BALL_RADIUS
      },
      score: {},
      status: 'waiting',
      gameArea: {
        width: this.GAME_WIDTH,
        height: this.GAME_HEIGHT
      }
    };
  }

  start() {
    const server = Deno.serve({ port: this.port }, (req) => {
      if (req.headers.get("upgrade") !== "websocket") {
        return new Response(null, { status: 501 });
      }

      const { socket, response } = Deno.upgradeWebSocket(req);
      this.handleConnection(socket);
      return response;
    });

    console.log(`Game server running on port ${this.port}`);
    this.startGameLoop();
  }

  private handleConnection(socket: WebSocket) {
    if (this.players.size >= this.MAX_PLAYERS) {
      socket.close(1000, "Game is full");
      return;
    }

    const playerId = Date.now() + Math.random();

    socket.onopen = () => {
      console.log(`Player ${playerId} connected`);

      const isLeftPlayer = this.players.size === 0;
      const paddleX = isLeftPlayer ? this.PADDLE_WIDTH : this.GAME_WIDTH - this.PADDLE_WIDTH * 2;

      const player: Player = {
        id: playerId,
        paddle: {
          y: this.GAME_HEIGHT / 2 - this.PADDLE_HEIGHT / 2,
          height: this.PADDLE_HEIGHT,
          speed: this.PADDLE_SPEED
        },
        socket: socket,
        input: 'stop'
      };

      this.players.set(playerId, player);
      this.gameState.score[playerId] = 0;
      this.updateGameState();

      socket.send(JSON.stringify({
        type: "connected",
        playerId: playerId,
        playerSide: isLeftPlayer ? 'left' : 'right'
      }));

      if (this.players.size === this.MAX_PLAYERS) {
        this.gameState.status = 'playing';
        console.log("Game started with 2 players");
      }
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handlePlayerInput(playerId, data);
      } catch (error) {
        console.error("Error parsing message:", error);
      }
    };

    socket.onclose = () => {
      console.log(`Player ${playerId} disconnected`);
      this.players.delete(playerId);
      delete this.gameState.score[playerId];

      if (this.players.size < this.MAX_PLAYERS) {
        this.gameState.status = 'waiting';
        this.resetBall();
      }

      this.updateGameState();
    };

    socket.onerror = (error) => {
      console.error(`WebSocket error for player ${playerId}:`, error);
    };
  }

  private handlePlayerInput(playerId: number, data: any) {
    const player = this.players.get(playerId);
    if (!player || data.type !== "input") return;

    if (data.action === 'up' || data.action === 'down' || data.action === 'stop') {
      player.input = data.action;
    }
  }

  private updateGameState() {
    this.gameState.players = {};
    for (const [id, player] of this.players) {
      this.gameState.players[id] = {
        y: player.paddle.y,
        height: player.paddle.height,
        speed: player.paddle.speed
      };
    }
  }

  private resetBall() {
    this.gameState.ball.x = this.GAME_WIDTH / 2;
    this.gameState.ball.y = this.GAME_HEIGHT / 2;
    this.gameState.ball.vx = this.BALL_INITIAL_SPEED * (Math.random() > 0.5 ? 1 : -1);
    this.gameState.ball.vy = this.BALL_INITIAL_SPEED * (Math.random() > 0.5 ? 1 : -1);
  }

  private updatePaddles() {
    for (const player of this.players.values()) {
      if (player.input === 'up') {
        player.paddle.y = Math.max(0, player.paddle.y - player.paddle.speed);
      } else if (player.input === 'down') {
        player.paddle.y = Math.min(
          this.GAME_HEIGHT - player.paddle.height,
          player.paddle.y + player.paddle.speed
        );
      }
    }
  }

  private updateBall() {
    if (this.gameState.status !== 'playing') return;

    this.gameState.ball.x += this.gameState.ball.vx;
    this.gameState.ball.y += this.gameState.ball.vy;

    if (this.gameState.ball.y <= this.BALL_RADIUS ||
        this.gameState.ball.y >= this.GAME_HEIGHT - this.BALL_RADIUS) {
      this.gameState.ball.vy = -this.gameState.ball.vy;
    }

    this.checkPaddleCollisions();
    this.checkGoals();
  }

  private checkPaddleCollisions() {
    const ball = this.gameState.ball;
    const players = Array.from(this.players.values());

    if (players.length < 2) return;

    const leftPlayer = players[0];
    const rightPlayer = players[1];

    if (ball.x - ball.radius <= this.PADDLE_WIDTH * 2 && ball.vx < 0) {
      if (ball.y >= leftPlayer.paddle.y &&
          ball.y <= leftPlayer.paddle.y + leftPlayer.paddle.height) {
        ball.vx = -ball.vx;
        ball.x = this.PADDLE_WIDTH * 2 + ball.radius;

        const relativeIntersectY = (leftPlayer.paddle.y + leftPlayer.paddle.height / 2) - ball.y;
        const normalizedIntersectY = relativeIntersectY / (leftPlayer.paddle.height / 2);
        ball.vy = -normalizedIntersectY * this.BALL_INITIAL_SPEED;
      }
    }

    if (ball.x + ball.radius >= this.GAME_WIDTH - this.PADDLE_WIDTH * 2 && ball.vx > 0) {
      if (ball.y >= rightPlayer.paddle.y &&
          ball.y <= rightPlayer.paddle.y + rightPlayer.paddle.height) {
        ball.vx = -ball.vx;
        ball.x = this.GAME_WIDTH - this.PADDLE_WIDTH * 2 - ball.radius;

        const relativeIntersectY = (rightPlayer.paddle.y + rightPlayer.paddle.height / 2) - ball.y;
        const normalizedIntersectY = relativeIntersectY / (rightPlayer.paddle.height / 2);
        ball.vy = -normalizedIntersectY * this.BALL_INITIAL_SPEED;
      }
    }
  }

  private checkGoals() {
    const ball = this.gameState.ball;
    const players = Array.from(this.players.values());

    if (players.length < 2) return;

    if (ball.x < 0) {
      this.gameState.score[players[1].id]++;
      console.log(`Right player scored! Score: ${this.gameState.score[players[0].id]} - ${this.gameState.score[players[1].id]}`);
      this.resetBall();
    } else if (ball.x > this.GAME_WIDTH) {
      this.gameState.score[players[0].id]++;
      console.log(`Left player scored! Score: ${this.gameState.score[players[0].id]} - ${this.gameState.score[players[1].id]}`);
      this.resetBall();
    }
  }

  private startGameLoop() {
    this.gameLoop = setInterval(() => {
      this.updatePaddles();
      this.updateBall();
      this.updateGameState();
      this.broadcastGameState();
    }, 1000 / 60);
  }

  private broadcastGameState() {
    if (this.players.size === 0) return;

    const message = JSON.stringify({
      type: "gameState",
      data: this.gameState
    });

    for (const player of this.players.values()) {
      if (player.socket.readyState === WebSocket.OPEN) {
        player.socket.send(message);
      }
    }
  }
}

const gameServer = new GameServer(8001);
gameServer.start();
