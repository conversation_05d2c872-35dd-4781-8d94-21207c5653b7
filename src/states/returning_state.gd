class_name ReturningState
extends UnitState

func enter() -> void:
	unit.target = unit.owner_node

func process(_delta: float) -> UnitState:
	if not is_instance_valid(unit.target):
		return IdleState.new(unit)

	unit.velocity = unit.global_position.direction_to(unit.target.global_position) * unit.speed
	if unit.global_position.distance_to(unit.target.global_position) < 30:
		if is_instance_valid(unit.mass_component):
			unit.owner_node.add_mass(unit.mass_component.mass)
		unit.queue_free()

	return null
