class_name ChasingState
extends UnitState

func process(_delta: float) -> UnitState:
	if not is_instance_valid(unit.target):
		unit.target = null
		match unit.type:
			UnitData.Type.DEFENDER:
				return OrbitingState.new(unit)
			UnitData.Type.HUNTER:
				return SeekingState.new(unit)
			UnitData.Type.AGGRESSOR:
				var enemy_group: StringName = "player2_mother" if unit.owner_node.player_id == 1 else "player1_mother"
				var enemies: Array[Node] = unit.get_tree().get_nodes_in_group(enemy_group)
				if not enemies.is_empty():
					unit.target = enemies[0]
				else:
					return IdleState.new(unit)

	if is_instance_valid(unit.target):
		unit.velocity = unit.global_position.direction_to(unit.target.global_position) * unit.speed

	return null
