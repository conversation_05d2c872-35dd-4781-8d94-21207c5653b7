class_name OrbitingState
extends UnitState

func process(delta: float) -> UnitState:
	unit.target = TargetingService.find_closest_enemy_in_radius(unit.global_position, unit.orbit_distance, unit.owner_node.player_id)
	if is_instance_valid(unit.target):
		return ChasingState.new(unit)

	unit.orbit_angle += 0.5 * delta
	var orbit_pos: Vector2 = unit.owner_node.global_position + Vector2.RIGHT.rotated(unit.orbit_angle) * unit.orbit_distance
	var direction_to_orbit_pos: Vector2 = unit.global_position.direction_to(orbit_pos)
	var desired_velocity: Vector2 = direction_to_orbit_pos * unit.speed
	unit.velocity = unit.velocity.lerp(desired_velocity, delta * 5.0)

	return null
