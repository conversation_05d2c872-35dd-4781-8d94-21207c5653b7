class_name MassBasedScalerComponent
extends Node

var _owner_node: Node2D
var _initial_scale: Vector2
var _base_mass: float
var _mass_component: MassComponent

func initialize(owner_node: Node2D, base_mass: float, mass_component: MassComponent = null) -> void:
	_owner_node = owner_node
	_initial_scale = owner_node.scale
	_base_mass = max(base_mass, 0.001)
	_set_mass_component(mass_component)

func update_scale(current_mass: float) -> void:
	if not is_instance_valid(_owner_node):
		return

	var mass_factor: float = sqrt(current_mass / _base_mass)
	var clamped_factor: float = min(mass_factor, GameSettings.max_scale_from_mass)
	_owner_node.scale = _initial_scale * clamped_factor

func _on_mass_changed(new_mass: float) -> void:
	update_scale(new_mass)

func _set_mass_component(mass_component: MassComponent) -> void:
	var callable := Callable(self, "_on_mass_changed")
	if _mass_component and _mass_component.mass_changed.is_connected(callable):
		_mass_component.mass_changed.disconnect(callable)
	_mass_component = mass_component
	if _mass_component:
		_mass_component.mass_changed.connect(callable)
		_on_mass_changed(_mass_component.mass)

func _exit_tree() -> void:
	_set_mass_component(null)
