extends Node

var current_scene: Node = null

func _ready() -> void:
	var root: Node = get_tree().root
	current_scene = root.get_child(root.get_child_count() - 1)

func goto_scene(packed_scene: PackedScene) -> void:
	if is_instance_valid(current_scene):
		current_scene.queue_free()

	current_scene = packed_scene.instantiate()

	get_tree().root.add_child(current_scene)

func quit_game() -> void:
	get_tree().quit()
