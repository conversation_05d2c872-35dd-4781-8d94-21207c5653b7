class_name <PERSON><PERSON>ell
extends CharacterBody2D


@export var combat_component: CombatComponent
@export var scaler_component: MassBasedScalerComponent
@export var sprite: Sprite2D
@export var mass_component: MassComponent

var type: UnitData.Type
var owner_node: MotherCell
var target: Node2D = null

var current_state: UnitState
var behavior: UnitBehavior

var speed: float = 150.0
var orbit_distance: float = 150.0
var orbit_angle: float = 0.0

func _ready() -> void:
	combat_component.combat_owner = self
	mass_component.died.connect(_on_mass_depleted)

func initialize(mother_cell: MotherCell, unit_data: UnitData) -> void:
	self.owner_node = mother_cell

	self.type = unit_data.unit_type
	sprite.texture = unit_data.texture

	if is_instance_valid(mass_component):
		mass_component.initialize(unit_data.cost)
		mass_component.died.connect(_on_mass_depleted)

	scaler_component.initialize(self, unit_data.cost, mass_component)

	combat_component.combat_owner = self
	combat_component.owner_mass_component = mass_component

	var mother_cell_combat_component: CombatComponent = mother_cell.combat_component
	combat_component.collision_layer = mother_cell_combat_component.collision_layer
	combat_component.collision_mask = mother_cell_combat_component.collision_mask

	behavior = unit_data.behavior
	if behavior:
		current_state = behavior.get_initial_state(self)
		if current_state:
			current_state.enter()
	else:
		current_state = IdleState.new(self)
		current_state.enter()

func _change_state(new_state: UnitState) -> void:
	if current_state:
		current_state.exit()
	current_state = new_state
	if current_state:
		current_state.enter()

func _physics_process(delta: float) -> void:
	if not is_instance_valid(owner_node):
		queue_free()
		return

	if InputManager.is_action_pressed(owner_node.player_id, "recall"):
		if not (current_state is RecallingState):
			_change_state(RecallingState.new(self))

	if current_state:
		var new_state: UnitState = current_state.process(delta)
		if new_state:
			_change_state(new_state)

	combat_component.process_combat(delta)
	move_and_slide()


func _on_mass_depleted() -> void:
	queue_free()
