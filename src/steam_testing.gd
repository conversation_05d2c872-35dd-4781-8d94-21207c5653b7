extends Control

const ACHIEVEMENT_NAME := "PLAY_ONE_GAME"
const ACHIEVEMENT_INDEX := 0
const STAT_NAME := "games_played"

@export var get_achievement: Button
@export var reset_achievements: Button
@export var achievement_title: Label
@export var achievement_image: TextureRect
@export var achievement_unlocked: Label

@export var user_avatar: TextureRect
@export var user_name: Label

@export var stats_label: Label

var timer: Timer

func _ready() -> void:
	get_achievement.pressed.connect(_on_get_achievement_pressed)
	reset_achievements.pressed.connect(_on_reset_achievements_pressed)
	Steam.user_achievement_icon_fetched.connect(_on_user_achievement_icon_fetched)
	Steam.avatar_loaded.connect(_on_avatar_loaded)
	Steam.global_stats_received.connect(_on_global_stats_received)
	user_name.text = Steam.getPersonaName()
	Steam.getPlayerAvatar()
	Steam.requestGlobalStats(1)
	achievement_title.text = Steam.getAchievementDisplayAttribute(ACHIEVEMENT_NAME, "name")
	var icon_handle: int = Steam.getAchievementIcon(ACHIEVEMENT_NAME)
	if icon_handle != 0:
		_on_user_achievement_icon_fetched(0, ACHIEVEMENT_NAME, false, icon_handle)
	_update_achievement_status()

	timer = Timer.new()
	timer.wait_time = 5.0
	timer.timeout.connect(_update_achievement_status)
	timer.autostart = true
	add_child(timer)

func _on_global_stats_received(_game_id: int, result: String) -> void:
	if result == "ok":
		stats_label.text = "Total matches played: %s" % Steam.getGlobalStatInt(STAT_NAME)
	else:
		print("Error receiving global statistics: ", result)

func _on_user_achievement_icon_fetched(_game_id: int, _achievement_name: String, _was_achieved: bool, icon_handle: int) -> void:
	var icon_size: Dictionary = Steam.getImageSize(icon_handle)
	var icon_buffer: Dictionary = Steam.getImageRGBA(icon_handle)
	var icon_image: Image = Image.create_from_data(icon_size.width as int, icon_size.height as int, false, Image.FORMAT_RGBA8, icon_buffer["buffer"] as PackedByteArray)
	var icon_texture: ImageTexture = ImageTexture.create_from_image(icon_image)
	achievement_image.texture = icon_texture

func _on_avatar_loaded(user_id: int, avatar_size: int, avatar_buffer: PackedByteArray) -> void:
	print("Avatar for user: %s" % user_id)
	print("Size: %s" % avatar_size)

	var avatar_image: Image = Image.create_from_data(avatar_size, avatar_size, false, Image.FORMAT_RGBA8, avatar_buffer)

	if avatar_size > 128:
		avatar_image.resize(128, 128, Image.INTERPOLATE_LANCZOS)

	var avatar_texture: ImageTexture = ImageTexture.create_from_image(avatar_image)

	user_avatar.texture = avatar_texture


func _on_get_achievement_pressed() -> void:
	var achievement: Dictionary = Steam.getAchievement(ACHIEVEMENT_NAME)
	if achievement.achieved:
		print("Achievement is already achieved")
		return

	Steam.setAchievement(ACHIEVEMENT_NAME)
	Steam.storeStats()

func _on_reset_achievements_pressed() -> void:
	Steam.clearAchievement(ACHIEVEMENT_NAME)
	Steam.storeStats()

func _update_achievement_status() -> void:
	var achievement: Dictionary = Steam.getAchievement(ACHIEVEMENT_NAME)
	if achievement.achieved:
		achievement_unlocked.text = "Achieved"
	else:
		achievement_unlocked.text = "Unachieved"
