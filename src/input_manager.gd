extends Node

const DOUBLE_TAP_DELAY_SEC: float = 0.3
var player_action_states: Dictionary = {
	1: {"create": {"pressed": false, "just_pressed": false, "just_released": false},
		 "recall": {"pressed": false, "just_pressed": false, "just_released": false}},
	2: {"create": {"pressed": false, "just_pressed": false, "just_released": false},
		 "recall": {"pressed": false, "just_pressed": false, "just_released": false}}
}
var _prev_player_action_states: Dictionary

var _last_tap_time_ms: Dictionary = {1: 0, 2: 0}
var _tap_count: Dictionary = {1: 0, 2: 0}
var _player_last_input_position: Dictionary = {1: Vector2.ZERO, 2: Vector2.ZERO}


func _ready() -> void:
	_prev_player_action_states = player_action_states.duplicate(true)


func _process(_delta: float) -> void:
	for player_id: int in player_action_states:
		for action_name: String in player_action_states[player_id]:
			var current_state: Dictionary = player_action_states[player_id][action_name]
			var prev_state: Dictionary = _prev_player_action_states[player_id][action_name]

			current_state.just_pressed = current_state.pressed and not prev_state.pressed
			current_state.just_released = not current_state.pressed and prev_state.pressed

	_prev_player_action_states = player_action_states.duplicate(true)

	var current_time_ms: int = Time.get_ticks_msec()
	for player_id: int in _tap_count:
		if _tap_count[player_id] == 1 and (current_time_ms - _last_tap_time_ms[player_id]) > DOUBLE_TAP_DELAY_SEC * 1000:
			_tap_count[player_id] = 0


func _unhandled_input(event: InputEvent) -> void:
	if event is InputEventKey:
		for player_id: int in [1, 2]:
			for action_name: String in ["create", "recall"]:
				var input_map_action: String = "p%d_%s" % [player_id, action_name]
				if event.is_action(input_map_action):
					player_action_states[player_id][action_name].pressed = event.is_pressed()

	if event is InputEventMouseButton or event is InputEventScreenTouch:
		if event.is_pressed() or event.is_released():
			var screen_width: float = get_viewport().get_visible_rect().size.x
			var position: Vector2
			if event is InputEventMouseButton:
				position = (event as InputEventMouseButton).position
			elif event is InputEventScreenTouch:
				position = (event as InputEventScreenTouch).position
			else:
				return

			var player_id: int = 1 if position.x < screen_width / 2 else 2
			_player_last_input_position[player_id] = _to_world_position(position)

			player_action_states[player_id]["create"].pressed = event.is_pressed()

			var current_time_ms: int = Time.get_ticks_msec()

			if event.is_pressed():
				if (current_time_ms - _last_tap_time_ms[player_id]) < DOUBLE_TAP_DELAY_SEC * 1000:
					_tap_count[player_id] += 1
				else:
					_tap_count[player_id] = 1

				_last_tap_time_ms[player_id] = current_time_ms

				if _tap_count[player_id] >= 2:
					player_action_states[player_id]["recall"].pressed = true

			if event.is_released():
				player_action_states[player_id]["recall"].pressed = false

	if event is InputEventMouseMotion:
		var screen_width: float = get_viewport().get_visible_rect().size.x
		var mouse_event: InputEventMouseMotion = event as InputEventMouseMotion
		var player_id: int = 1 if mouse_event.position.x < screen_width / 2 else 2
		_player_last_input_position[player_id] = _to_world_position(mouse_event.position)

	if event is InputEventScreenDrag:
		var screen_width: float = get_viewport().get_visible_rect().size.x
		var screen_drag_event: InputEventScreenDrag = event as InputEventScreenDrag
		var player_id: int = 1 if screen_drag_event.position.x < screen_width / 2 else 2
		_player_last_input_position[player_id] = _to_world_position(screen_drag_event.position)

func is_action_pressed(player_id: int, action_name: String) -> bool:
	return player_action_states[player_id][action_name].pressed

func is_action_just_pressed(player_id: int, action_name: String) -> bool:
	return player_action_states[player_id][action_name].just_pressed

func is_action_just_released(player_id: int, action_name: String) -> bool:
	return player_action_states[player_id][action_name].just_released

func get_last_input_position(player_id: int) -> Vector2:
	return _player_last_input_position.get(player_id, Vector2.ZERO)

func _to_world_position(position: Vector2) -> Vector2:
	var viewport := get_viewport()
	if viewport:
		return viewport.get_canvas_transform().affine_inverse() * position
	return position
