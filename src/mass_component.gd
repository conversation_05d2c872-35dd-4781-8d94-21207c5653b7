class_name MassComponent
extends Node

signal mass_changed(new_mass: float)
signal died

@export var initial_mass: float = 100.0

var _mass: float = 0.0
var mass: float:
	get:
		return _mass
	set(value):
		var old_mass: float = _mass
		_mass = max(value, 0.0)
		if not is_equal_approx(_mass, old_mass):
			mass_changed.emit(_mass)
		if _mass <= 0.0 and old_mass > 0.0:
			died.emit()

func _ready() -> void:
	if _mass <= 0.0:
		mass = initial_mass

func initialize(initial_value: float) -> void:
	initial_mass = initial_value
	mass = initial_value

func take_damage(amount: float) -> void:
	if amount <= 0.0:
		return
	mass = _mass - amount

func add_mass(amount: float) -> void:
	if amount == 0.0:
		return
	mass = _mass + amount

func is_alive() -> bool:
	return _mass > 0.0
