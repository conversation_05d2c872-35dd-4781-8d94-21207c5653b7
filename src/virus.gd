class_name Virus
extends CharacterBody2D

@export var combat_component: CombatComponent
@export var speed: float = 60.0
@export var scaler_component: MassBasedScalerComponent
@export var mass_component: MassComponent
@export var start_mass: float = 10.0

var target: Node2D = null

func _ready() -> void:
	add_to_group("virus")
	set_collision_layer_value(4, true)

	if is_instance_valid(scaler_component):
		scaler_component.initialize(self, start_mass, mass_component)

	if is_instance_valid(mass_component):
		mass_component.mass_changed.connect(_on_mass_changed)
		mass_component.died.connect(_on_mass_depleted)
		mass_component.initialize(start_mass)
	else:
		push_warning("Virus is missing a MassComponent")

	combat_component.combat_owner = self
	combat_component.owner_mass_component = mass_component

func _physics_process(delta: float) -> void:
	if is_instance_valid(mass_component) and mass_component.mass <= 1.0:
		queue_free()

	if not is_instance_valid(target): find_new_target()

	if target:
		velocity = global_position.direction_to(target.global_position) * speed * delta

	combat_component.process_combat(delta)

	move_and_slide()

func find_new_target() -> void:
	var p1_cells: Array[Node] = get_tree().get_nodes_in_group("player1")
	var p2_cells: Array[Node] = get_tree().get_nodes_in_group("player2")
	var all_cells: Array[Node] = p1_cells + p2_cells

	var closest: Node2D = null
	var min_dist_sq: float = INF
	for cell: Node2D in all_cells:
		var dist_sq: float = global_position.distance_squared_to(cell.global_position)
		if dist_sq < min_dist_sq:
			min_dist_sq = dist_sq
			closest = cell
	target = closest

func _on_mass_changed(new_mass: float) -> void:
	if is_instance_valid(scaler_component):
		scaler_component.update_scale(new_mass)

func _on_mass_depleted() -> void:
	queue_free()
