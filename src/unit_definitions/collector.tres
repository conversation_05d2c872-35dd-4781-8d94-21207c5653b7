[gd_resource type="Resource" script_class="UnitData" load_steps=4 format=3 uid="uid://collector"]

[ext_resource type="Script" uid="uid://bn1vunwhi56ot" path="res://src/unit_data.gd" id="1_xxxxx"]
[ext_resource type="Texture2D" uid="uid://ckcbris45bst6" path="res://src/units/jelly/harvester.png" id="2_yyyyy"]
[ext_resource type="Resource" path="res://src/unit_behaviors/collector_behavior.tres" id="3_behavior"]

[resource]
script = ExtResource("1_xxxxx")
unit_type = 0
texture = ExtResource("2_yyyyy")
cost = 10.0
behavior = ExtResource("3_behavior")
