[gd_resource type="Resource" script_class="UnitData" load_steps=4 format=3 uid="uid://d1hx4jr813il5"]

[ext_resource type="Script" uid="uid://bn1vunwhi56ot" path="res://src/unit_data.gd" id="1_xxxxx"]
[ext_resource type="Texture2D" uid="uid://cn2ij86nj8pwr" path="res://src/units/jelly/hunter.png" id="2_yyyyy"]
[ext_resource type="Resource" path="res://src/unit_behaviors/hunter_behavior.tres" id="3_behavior"]

[resource]
script = ExtResource("1_xxxxx")
unit_type = 2
texture = ExtResource("2_yyyyy")
cost = 60.0
behavior = ExtResource("3_behavior")
