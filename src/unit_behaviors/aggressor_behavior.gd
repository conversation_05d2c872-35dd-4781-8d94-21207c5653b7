class_name AggressorBehavior
extends UnitBehavior

func get_initial_state(child_cell: ChildCell) -> UnitState:
	var chasing_state: ChasingState = ChasingState.new(child_cell)

	var enemy_group: StringName = "player2_mother" if child_cell.owner_node.player_id == 1 else "player1_mother"
	var enemies: Array[Node] = child_cell.get_tree().get_nodes_in_group(enemy_group)
	if not enemies.is_empty():
		child_cell.target = enemies[0]

	return chasing_state
