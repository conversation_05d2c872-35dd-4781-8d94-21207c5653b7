[gd_scene load_steps=3 format=3 uid="uid://bwg4fmw4l2f0l"]

[ext_resource type="Script" uid="uid://ussp5hkx8hpv" path="res://src/steam_testing.gd" id="1_4yg50"]
[ext_resource type="Texture2D" uid="uid://bpewest1j6ei" path="res://src/icon.svg" id="2_ckwml"]

[node name="SteamTesting" type="Control" node_paths=PackedStringArray("get_achievement", "reset_achievements", "achievement_title", "achievement_image", "achievement_unlocked", "user_avatar", "user_name", "stats_label")]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_4yg50")
get_achievement = NodePath("CenterContainer/HBoxContainer/Achievements/GetAchievement")
reset_achievements = NodePath("CenterContainer/HBoxContainer/Achievements/ResetAchievements")
achievement_title = NodePath("CenterContainer/HBoxContainer/Achievements/AchievementTitle")
achievement_image = NodePath("CenterContainer/HBoxContainer/Achievements/Icon")
achievement_unlocked = NodePath("CenterContainer/HBoxContainer/Achievements/Status")
user_avatar = NodePath("CenterContainer/HBoxContainer/UserInfo/UserAvatar")
user_name = NodePath("CenterContainer/HBoxContainer/UserInfo/Username")
stats_label = NodePath("CenterContainer/HBoxContainer/Stats")

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="HBoxContainer" type="HBoxContainer" parent="CenterContainer"]
layout_mode = 2
theme_override_constants/separation = 200

[node name="Achievements" type="VBoxContainer" parent="CenterContainer/HBoxContainer"]
layout_mode = 2

[node name="GetAchievement" type="Button" parent="CenterContainer/HBoxContainer/Achievements"]
layout_mode = 2
text = "Get achievement"

[node name="ResetAchievements" type="Button" parent="CenterContainer/HBoxContainer/Achievements"]
layout_mode = 2
text = "Reset achievement"

[node name="AchievementTitle" type="Label" parent="CenterContainer/HBoxContainer/Achievements"]
layout_mode = 2
text = "Title"
horizontal_alignment = 1

[node name="Icon" type="TextureRect" parent="CenterContainer/HBoxContainer/Achievements"]
layout_mode = 2
size_flags_horizontal = 4
texture = ExtResource("2_ckwml")
stretch_mode = 4

[node name="Status" type="Label" parent="CenterContainer/HBoxContainer/Achievements"]
layout_mode = 2
text = "Locked"
horizontal_alignment = 1

[node name="UserInfo" type="VBoxContainer" parent="CenterContainer/HBoxContainer"]
layout_mode = 2
alignment = 1

[node name="UserAvatar" type="TextureRect" parent="CenterContainer/HBoxContainer/UserInfo"]
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
texture = ExtResource("2_ckwml")
stretch_mode = 4

[node name="Username" type="Label" parent="CenterContainer/HBoxContainer/UserInfo"]
layout_mode = 2
text = "Nickname"
horizontal_alignment = 1

[node name="Stats" type="Label" parent="CenterContainer/HBoxContainer"]
layout_mode = 2
text = "stat_name: counter"
