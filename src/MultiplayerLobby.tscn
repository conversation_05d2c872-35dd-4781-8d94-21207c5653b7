[gd_scene load_steps=4 format=3 uid="uid://dygxdsag8txus"]

[ext_resource type="Script" uid="uid://mf2lqayogyl0" path="res://src/MultiplayerLobby.gd" id="1_1a2b3"]
[ext_resource type="PackedScene" uid="uid://ceaoqapkk7l14" path="res://src/PongGame.tscn" id="2_i20d3"]
[ext_resource type="PackedScene" uid="uid://dnxgjm6x2bq08" path="res://src/main_menu.tscn" id="3_2w4ow"]

[node name="MultiplayerLobby" type="Control" node_paths=PackedStringArray("find_game_button", "status_label", "back_button")]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_1a2b3")
pong_game_scene = ExtResource("2_i20d3")
main_menu_scene = ExtResource("3_2w4ow")
find_game_button = NodePath("VBoxContainer/FindGameButton")
status_label = NodePath("VBoxContainer/StatusLabel")
back_button = NodePath("VBoxContainer/BackButton")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -75.0
offset_right = 100.0
offset_bottom = 75.0
grow_horizontal = 2
grow_vertical = 2

[node name="StatusLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Click to find a game"
horizontal_alignment = 1

[node name="FindGameButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "Find Game"

[node name="BackButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "Back to Menu"
