extends Control

@export var start_game_button: But<PERSON>
@export var steam_testing_button: Button
@export var exit_button: But<PERSON>
@export var multiplayer_button: Button
@export var fmod_button: Button
@export var game_scene: PackedScene
@export var steam_testing_scene: PackedScene
@export var multiplayer_lobby_scene: PackedScene
@export var fmod_sounds_scene: PackedScene

func _ready() -> void:
	start_game_button.pressed.connect(_on_start_game_button_pressed)
	steam_testing_button.pressed.connect(_on_steam_testing_button_pressed)
	exit_button.pressed.connect(_on_exit_button_pressed)
	multiplayer_button.pressed.connect(_on_multiplayer_button_pressed)
	fmod_button.pressed.connect(_on_fmod_button_pressed)

func _on_start_game_button_pressed() -> void:
	SceneManager.goto_scene(game_scene)

func _on_steam_testing_button_pressed() -> void:
	SceneManager.goto_scene(steam_testing_scene)

func _on_exit_button_pressed() -> void:
	SceneManager.quit_game()

func _on_multiplayer_button_pressed() -> void:
	SceneManager.goto_scene(multiplayer_lobby_scene)

func _on_fmod_button_pressed() -> void:
	SceneManager.goto_scene(fmod_sounds_scene)
