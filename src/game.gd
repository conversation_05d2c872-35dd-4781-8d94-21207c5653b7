class_name Game
extends Node2D

signal game_finished(winner_player_id: int)

@onready var player1_cell: MotherCell = $Player1Cell
@onready var player2_cell: MotherCell = $Player2Cell
@onready var food_spawn_timer: Timer = $FoodSpawnTimer
@onready var virus_spawn_timer: Timer = $VirusSpawnTimer

var screen_size: Vector2

@export var food_definitions: Array[FoodData]

const FoodScene = preload("res://src/food.tscn")
const VirusScene = preload("res://src/virus.tscn")

func _ready() -> void:
	screen_size = get_viewport_rect().size

	player1_cell.died.connect(_on_player_died.bind(1))
	player2_cell.died.connect(_on_player_died.bind(2))

	food_spawn_timer.timeout.connect(_on_food_spawn_timer_timeout)
	virus_spawn_timer.timeout.connect(_on_virus_spawn_timer_timeout)

func _on_food_spawn_timer_timeout() -> void:
	if food_definitions.is_empty():
		printerr("Food Definitions array is empty in Main node. Cannot spawn food.")
		return

	var random_food_data: FoodData = food_definitions.pick_random()
	var food: Food = FoodScene.instantiate()
	food.initialize(random_food_data)
	food.position = Vector2(randf_range(50, screen_size.x - 50), randf_range(50, screen_size.y - 50))
	add_child(food)

func _on_virus_spawn_timer_timeout() -> void:
	var virus: Virus = VirusScene.instantiate()
	var spawn_pos: Vector2 = Vector2()
	if randi() % 2 == 0:
		spawn_pos.x = randf_range(0, screen_size.x)
		spawn_pos.y = [0.0, screen_size.y][randi() % 2]
	else:
		spawn_pos.x = [0.0, screen_size.x][randi() % 2]
		spawn_pos.y = randf_range(0, screen_size.y)
	virus.position = spawn_pos
	add_child(virus)

func _on_player_died(player_num: int) -> void:
	print("Player %d died" % player_num)
	var winner_id: int = 2 if player_num == 1 else 1

	game_finished.emit(winner_id)
