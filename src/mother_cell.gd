class_name MotherCell
extends Area2D

@export var player_id: int = 1

@export var start_mass: float = 200.0
@export var combat_component: CombatComponent
@export var scaler_component: MassBasedScalerComponent
@export var mass_component: MassComponent

var charge_time: float = 0.0
var is_charging: bool = false

@export var charge_thresholds: Array[float] = [0.25, 0.75, 1.5, 2.5]
@export var unit_definitions: Array[UnitData]

const ChildCellScene = preload("res://src/child_cell.tscn")

signal died

@onready var spawn_preview_sprite: Sprite2D = $SpawnPreviewSprite
@onready var mass_label: Label = $MassLabel
@onready var charge_bar: ProgressBar = $ChargeBar

func _ready() -> void:
	if is_instance_valid(combat_component):
		combat_component.owner_mass_component = mass_component

	if is_instance_valid(scaler_component):
		scaler_component.initialize(self, start_mass, mass_component)

	if is_instance_valid(mass_component):
		mass_component.mass_changed.connect(_on_mass_changed)
		mass_component.died.connect(_on_mass_component_died)
		mass_component.initialize(start_mass)
	else:
		push_warning("Mother<PERSON><PERSON> is missing a MassComponent.")
	charge_bar.max_value = charge_thresholds.back()
	charge_bar.value = 0
	charge_bar.visible = false

	if player_id == 1:
		collision_layer = 1
		collision_mask = 0
		add_to_group("player1")
		add_to_group("player1_mother")
	else:
		collision_layer = 1 << 1
		collision_mask = 0
		add_to_group("player2")
		add_to_group("player2_mother")

func _process(delta: float) -> void:
	if is_charging:
		charge_time += delta
		charge_bar.value = charge_time
		_update_spawn_preview()

	if InputManager.is_action_just_pressed(player_id, "create"):
		is_charging = true
		charge_bar.visible = true
		charge_time = 0
		spawn_preview_sprite.visible = true
		_update_spawn_preview()

	if InputManager.is_action_just_released(player_id, "create"):
		if is_charging:
			var input_position: Vector2 = InputManager.get_last_input_position(player_id)
			print("input_position: ", input_position)
			var direction: Vector2 = (input_position - global_position)
			print("direction: ", direction)
			if direction.length_squared() > 0:
				direction = direction.normalized()
			else:
				direction = Vector2.RIGHT
			spawn_unit(direction)
			is_charging = false
			charge_bar.visible = false
			charge_time = 0
			spawn_preview_sprite.visible = false

func _physics_process(delta: float) -> void:
	if is_instance_valid(combat_component):
		combat_component.process_combat(delta)


func spawn_unit(direction: Vector2) -> void:
	var unit_data: UnitData = _get_unit_data_for_charge_time()
	if unit_data == null:
		printerr("Unit definition not found for charge time: ", charge_time)
		return

	var cost: float = unit_data.cost
	var available_mass: float = mass_component.mass if is_instance_valid(mass_component) else 0.0

	if available_mass >= cost:
		mass_component.add_mass(-cost)
		var new_cell: ChildCell = ChildCellScene.instantiate()

		var spawn_offset: Vector2 = direction * (scale.x * 200)
		new_cell.global_position = global_position + spawn_offset
		get_parent().add_child(new_cell)
		new_cell.initialize(self, unit_data)

func add_mass(amount: float) -> void:
	if is_instance_valid(mass_component):
		mass_component.add_mass(amount)

func _get_unit_data_for_charge_time() -> UnitData:
	if unit_definitions.is_empty():
		return null

	var thresholds_to_check: int = min(charge_thresholds.size(), unit_definitions.size())
	var unit_type_index: int = -1
	for i in range(thresholds_to_check):
		if charge_time < charge_thresholds[i]:
			unit_type_index = i
			break

	if unit_type_index == -1:
		unit_type_index = min(thresholds_to_check, unit_definitions.size() - 1)

	if unit_type_index < 0 or unit_type_index >= unit_definitions.size():
		return null

	return unit_definitions[unit_type_index]

func _update_spawn_preview() -> void:
	if not is_instance_valid(spawn_preview_sprite):
		return

	var unit_data: UnitData = _get_unit_data_for_charge_time()
	if unit_data == null:
		spawn_preview_sprite.visible = false
		return

	spawn_preview_sprite.texture = unit_data.texture
	spawn_preview_sprite.scale = Vector2(0.5, 0.5)

	var input_position: Vector2 = InputManager.get_last_input_position(player_id)
	var direction: Vector2 = input_position - global_position
	if direction.length_squared() > 0:
		direction = direction.normalized()
	else:
		direction = Vector2.RIGHT

	var spawn_offset: Vector2 = direction * (scale.x * 200)
	spawn_preview_sprite.position = spawn_offset
	spawn_preview_sprite.visible = true

func _on_mass_changed(new_mass: float) -> void:
	if is_instance_valid(scaler_component):
		scaler_component.update_scale(new_mass)
	mass_label.text = str(int(new_mass))

func _on_mass_component_died() -> void:
	print("Mother cell died")
	emit_signal("died")
	queue_free()
