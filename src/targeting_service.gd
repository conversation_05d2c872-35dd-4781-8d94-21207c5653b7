extends Node

func find_closest_food(position: Vector2) -> Node2D:
	return find_closest_in_group(position, "food")

func find_closest_enemy_in_radius(position: Vector2, radius: float, owner_player_id: int) -> Node2D:
	var enemy_groups: Array = ["player2", "virus"] if owner_player_id == 1 else ["player1", "virus"]
	var potential_targets: Array[Node] = []
	for group: StringName in enemy_groups:
		potential_targets.append_array(get_tree().get_nodes_in_group(group))
	return find_closest(position, potential_targets, radius)

func find_closest_in_group(position: Vector2, group_name: String) -> Node2D:
	return find_closest(position, get_tree().get_nodes_in_group(group_name))

func find_closest(position: Vector2, nodes: Array, max_dist: float = INF) -> Node2D:
	var closest: Node2D = null
	var min_dist_sq: float = max_dist * max_dist
	for node: Node2D in nodes:
		var dist_sq: float = position.distance_squared_to(node.global_position)
		if dist_sq < min_dist_sq:
			min_dist_sq = dist_sq
			closest = node
	return closest
