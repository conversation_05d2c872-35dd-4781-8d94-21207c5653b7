extends Control

@export var play_sound_button: But<PERSON>
@export var play_gameplay_music_button: But<PERSON>
@export var play_menu_music_button: Button
@export var stop_menu_music_button: But<PERSON>

@export var gameplay_music_emitter: FmodEventEmitter2D
@export var menu_music_emitter: FmodEventEmitter2D
@export var sounds_emitter: FmodEventEmitter2D

func _ready() -> void:
	play_sound_button.pressed.connect(_on_play_sound_button_pressed)
	play_gameplay_music_button.pressed.connect(_on_play_gameplay_music_button_pressed)
	play_menu_music_button.pressed.connect(_on_play_menu_music_button_pressed)
	stop_menu_music_button.pressed.connect(_on_stop_menu_music_button_pressed)

func _on_play_sound_button_pressed() -> void:
	sounds_emitter.play()

func _on_play_gameplay_music_button_pressed() -> void:
	menu_music_emitter.stop()
	gameplay_music_emitter.play()

func _on_play_menu_music_button_pressed() -> void:
	gameplay_music_emitter.stop()
	menu_music_emitter.play()

func _on_stop_menu_music_button_pressed() -> void:
	menu_music_emitter.stop()
	gameplay_music_emitter.stop()
